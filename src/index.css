
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 216 33% 10%;
    --foreground: 210 40% 98%;

    --card: 216 33% 15%;
    --card-foreground: 210 40% 98%;

    --popover: 216 33% 15%;
    --popover-foreground: 210 40% 98%;

    --primary: 199 94% 48%;
    --primary-foreground: 210 40% 98%;

    --secondary: 260 86% 74%;
    --secondary-foreground: 210 40% 98%;

    --muted: 216 33% 20%;
    --muted-foreground: 210 40% 70%;

    --accent: 199 85% 56%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 216 33% 20%;
    --input: 216 33% 20%;
    --ring: 199 94% 48%;

    --radius: 0.5rem;

    --sidebar-background: 216 33% 17%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 199 94% 48%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 216 33% 22%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 216 33% 25%;
    --sidebar-ring: 199 94% 48%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  .font-mono {
    font-family: 'JetBrains Mono', monospace;
  }
}

/* Custom styles for our force plate app */
@layer components {
  .data-panel {
    @apply bg-card rounded-md border border-border p-4;
  }
  
  .value-box {
    @apply font-mono bg-muted px-3 py-2 rounded text-foreground inline-block;
  }
  
  .animate-connection {
    @apply animate-pulse-gentle bg-forcePlate-accent;
  }
}
