import { useState, useEffect, useCallback } from 'react';

interface PanelSizes {
  sidebar: number;
  visualization: number;
  plots: number;
}

const DEFAULT_PANEL_SIZES: PanelSizes = {
  sidebar: 20,
  visualization: 60,
  plots: 40,
};

const STORAGE_KEY = 'force-plate-panel-sizes';

export function usePanelSizes() {
  const [panelSizes, setPanelSizes] = useState<PanelSizes>(DEFAULT_PANEL_SIZES);

  // Load panel sizes from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedSizes = JSON.parse(stored);
        setPanelSizes({ ...DEFAULT_PANEL_SIZES, ...parsedSizes });
      }
    } catch (error) {
      console.warn('Failed to load panel sizes from localStorage:', error);
    }
  }, []);

  // Save panel sizes to localStorage
  const savePanelSizes = useCallback((sizes: Partial<PanelSizes>) => {
    const newSizes = { ...panelSizes, ...sizes };
    setPanelSizes(newSizes);
    
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newSizes));
    } catch (error) {
      console.warn('Failed to save panel sizes to localStorage:', error);
    }
  }, [panelSizes]);

  // Reset to default sizes
  const resetPanelSizes = useCallback(() => {
    setPanelSizes(DEFAULT_PANEL_SIZES);
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to remove panel sizes from localStorage:', error);
    }
  }, []);

  return {
    panelSizes,
    savePanelSizes,
    resetPanelSizes,
  };
}
