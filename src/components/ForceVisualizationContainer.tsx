import React, { useState, useEffect } from 'react';
import ForceVisualization from './ForceVisualization';
import { PLATE_HOVER_EVENT } from '@/lib/plateHoverEvent';
import { useContainerSize } from '@/hooks/use-container-size';

// Container component to handle the hover events and container resizing
const ForceVisualizationContainer: React.FC = () => {
  const [hoveredPlateId, setHoveredPlateId] = useState<string | null>(null);
  const [containerRef, containerSize] = useContainerSize();

  useEffect(() => {
    // Listen for custom hover events from the sidebar
    const handlePlateHover = (e: Event) => {
      const customEvent = e as CustomEvent;
      setHoveredPlateId(customEvent.detail.plateId);
    };

    document.addEventListener(PLATE_HOVER_EVENT, handlePlateHover);

    return () => {
      document.removeEventListener(PLATE_HOVER_EVENT, handlePlateHover);
    };
  }, []);

  return (
    <div ref={containerRef} className="w-full h-full">
      <ForceVisualization
        hoveredPlateId={hoveredPlateId}
        containerSize={containerSize}
      />
    </div>
  );
};

export default ForceVisualizationContainer;
