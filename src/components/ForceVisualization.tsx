
import React, { useRef, useMemo } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Grid, Html } from '@react-three/drei';
import * as THREE from 'three';
import { useSerial, ForceData, ForcePlate } from '@/context/SerialContext';

interface ContainerSize {
  width: number;
  height: number;
}

// --- Visualization Constants (for easy settings) ---
const FORCE_PLATE_SIZE = 0.5; // meters (side length)
const FORCE_PLATE_THICKNESS = 0.12; // meters
const FORCE_PLATE_SPACING = 0.6; // meters between plates
const FORCE_PLATE_ROTATION = [-Math.PI / 2, 0, 0] as [number, number, number];
const FORCE_PLATE_COLOR = "#444";
const FORCE_PLATE_METALNESS = 0.8;
const FORCE_PLATE_ROUGHNESS = 0.4;
const FORCE_PLATE_HIGHLIGHTED_COLOR = "#667";

const FORCE_VECTOR_MIN_LENGTH = 0.1; // meters
const FORCE_VECTOR_MAX_LENGTH = 0.5; // meters
const FORCE_VECTOR_SCALE = 0.0005; // scale factor for force magnitude
const FORCE_VECTOR_HEAD_LENGTH = 0.05; // meters
const FORCE_VECTOR_HEAD_WIDTH = 0.03; // meters
const FORCE_VECTOR_INFO_OFFSET = 0.1; // meters above vector

const COP_SCALE = 0.001; // mm to meters

const CANVAS_CAMERA_POSITION = [1.5, 1.3, 1.5] as [number, number, number];
const CANVAS_CAMERA_FOV = 45;
const CANVAS_BG_CLASS = "bg-forcePlate-dark";
const CANVAS_BORDER_CLASS = "border-forcePlate-accent/20";

const GRID_ARGS: [number, number] = [10, 10];
const GRID_CELL_COLOR = "#666";
const GRID_SECTION_COLOR = "#999";
const GRID_FADE_DISTANCE = 4;
const GRID_CELL_SIZE = 0.2;

// Axis indicator constants
const AXIS_INDICATOR_SIZE = 0.15; // Size of axis arrows
const AXIS_INDICATOR_POSITION: [number, number, number] = [1.2, -0.8, 1.2]; // Bottom-right corner
const AXIS_COLORS = {
  x: "#ff4444", // Red for X
  y: "#44ff44", // Green for Y
  z: "#4444ff"  // Blue for Z
};

type ForcePlateProps = {
  position: [number, number, number];
  scale?: number;
  isHighlighted: boolean;
  plate: ForcePlate;
  onClick?: () => void;
};

// Component for rendering the force plate base
const ForcePlateVisual: React.FC<ForcePlateProps> = ({ 
  position, 
  scale = 1, 
  isHighlighted, 
  plate, 
  onClick 
}) => {
  return (
    <mesh 
      position={position} 
      rotation={FORCE_PLATE_ROTATION} 
      onClick={onClick}
    >
      <boxGeometry 
        args={[FORCE_PLATE_SIZE * scale, FORCE_PLATE_SIZE * scale, FORCE_PLATE_THICKNESS * scale]} 
      />
      <meshStandardMaterial 
        color={isHighlighted ? FORCE_PLATE_HIGHLIGHTED_COLOR : FORCE_PLATE_COLOR} 
        metalness={FORCE_PLATE_METALNESS} 
        roughness={FORCE_PLATE_ROUGHNESS} 
      />
      <Html position={[0, 0, FORCE_PLATE_THICKNESS/2 * scale]}>
        <div className="px-2 py-1 bg-black/70 rounded text-white text-xs whitespace-nowrap">
          {plate.name}
        </div>
      </Html>
    </mesh>
  );
};

type ForceVectorProps = {
  position: [number, number, number];
  forceData: ForceData | null;
  scale?: number;
  color?: string;
};

// Component for rendering the force vector
const ForceVector: React.FC<ForceVectorProps> = ({ 
  position, 
  forceData, 
  scale = 1,
  color = "#0EA5E9"
}) => {
  const arrowRef = useRef<THREE.ArrowHelper>(null);
  
  // Calculate arrow properties based on force data
  const arrowProps = useMemo(() => {
    if (!forceData) {
      return {
        direction: new THREE.Vector3(0, 1, 0),
        length: FORCE_VECTOR_MIN_LENGTH,
      };
    }

    // Create normalized direction vector from force components
    const direction = new THREE.Vector3(
      forceData.fx,
      forceData.fz, // In Three.js, Y is up, but our Z is vertical force
      -forceData.fy  // Negate Y for correct orientation
    ).normalize();

    // Calculate magnitude for scaling (Pythagorean)
    const magnitude = Math.sqrt(
      forceData.fx * forceData.fx +
      forceData.fy * forceData.fy +
      forceData.fz * forceData.fz
    );
    
    // Scale for visualization (adjust as needed)
    const length = Math.min(Math.max(magnitude * FORCE_VECTOR_SCALE * scale, FORCE_VECTOR_MIN_LENGTH), FORCE_VECTOR_MAX_LENGTH * scale);
    
    return { direction, length };
  }, [forceData, scale]);

  // Information display above the vector
  const ForceInfo = () => {
    if (!forceData) return null;
    
    // Calculate magnitude for display
    const magnitude = Math.sqrt(
      forceData.fx * forceData.fx +
      forceData.fy * forceData.fy +
      forceData.fz * forceData.fz
    ).toFixed(1);
    
    // Calculate y-position above the vector
    const yOffset = position[1] + FORCE_VECTOR_INFO_OFFSET + arrowProps.length;
    
    return (
      <Html position={[position[0], yOffset, position[2]]}>
        <div className="text-xs font-mono bg-black/70 text-white px-2 py-1 rounded whitespace-nowrap">
          {magnitude} N
        </div>
      </Html>
    );
  };

  return (
    <group position={position}>
      <arrowHelper
        ref={arrowRef}
        args={[
          arrowProps.direction,
          new THREE.Vector3(0, 0, 0),
          arrowProps.length,
          color,
          FORCE_VECTOR_HEAD_LENGTH * scale,
          FORCE_VECTOR_HEAD_WIDTH * scale
        ]}
      />
      <ForceInfo />
    </group>
  );
};

// Component for rendering axis direction indicator
const AxisIndicator: React.FC = () => {
  return (
    <group position={AXIS_INDICATOR_POSITION}>
      {/* X-axis arrow (Red) */}
      <arrowHelper
        args={[
          new THREE.Vector3(1, 0, 0), // Direction: positive X
          new THREE.Vector3(0, 0, 0), // Origin
          AXIS_INDICATOR_SIZE,        // Length
          AXIS_COLORS.x,              // Color
          AXIS_INDICATOR_SIZE * 0.3,  // Head length
          AXIS_INDICATOR_SIZE * 0.2   // Head width
        ]}
      />
      <Html position={[AXIS_INDICATOR_SIZE * 1.2, 0, 0]}>
        <div className="text-xs font-bold text-red-400 select-none">X</div>
      </Html>

      {/* Y-axis arrow (Green) */}
      <arrowHelper
        args={[
          new THREE.Vector3(0, 1, 0), // Direction: positive Y (up)
          new THREE.Vector3(0, 0, 0), // Origin
          AXIS_INDICATOR_SIZE,        // Length
          AXIS_COLORS.y,              // Color
          AXIS_INDICATOR_SIZE * 0.3,  // Head length
          AXIS_INDICATOR_SIZE * 0.2   // Head width
        ]}
      />
      <Html position={[0, AXIS_INDICATOR_SIZE * 1.2, 0]}>
        <div className="text-xs font-bold text-green-400 select-none">Y</div>
      </Html>

      {/* Z-axis arrow (Blue) */}
      <arrowHelper
        args={[
          new THREE.Vector3(0, 0, 1), // Direction: positive Z
          new THREE.Vector3(0, 0, 0), // Origin
          AXIS_INDICATOR_SIZE,        // Length
          AXIS_COLORS.z,              // Color
          AXIS_INDICATOR_SIZE * 0.3,  // Head length
          AXIS_INDICATOR_SIZE * 0.2   // Head width
        ]}
      />
      <Html position={[0, 0, AXIS_INDICATOR_SIZE * 1.2]}>
        <div className="text-xs font-bold text-blue-400 select-none">Z</div>
      </Html>
    </group>
  );
};

// Main visualization component for all force plates
type ForceVisualizationProps = {
  hoveredPlateId: string | null;
  containerSize?: ContainerSize;
};

const ForceVisualization: React.FC<ForceVisualizationProps> = ({ hoveredPlateId, containerSize }) => {
  const { forcePlates } = useSerial();
  
  // Calculate positions for force plates in a grid layout
  const platePositions = useMemo(() => {
    const positions: Record<string, [number, number, number]> = {};
    const gridSize = Math.ceil(Math.sqrt(forcePlates.length));
    
    forcePlates.forEach((plate, index) => {
      // Position plates in a grid pattern
      const row = Math.floor(index / gridSize);
      const col = index % gridSize;
      
      positions[plate.id] = [
        (col - (gridSize-1)/2) * FORCE_PLATE_SPACING,
        FORCE_PLATE_THICKNESS / 2,
        (row - (gridSize-1)/2) * FORCE_PLATE_SPACING
      ];
    });
    
    return positions;
  }, [forcePlates]);

  return (
    <div
      className={`w-full h-full ${CANVAS_BG_CLASS} rounded-lg overflow-hidden border ${CANVAS_BORDER_CLASS}`}
    >
      <Canvas
        camera={{ position: CANVAS_CAMERA_POSITION, fov: CANVAS_CAMERA_FOV }}
        style={{ width: '100%', height: '100%' }}
        resize={{ scroll: false, debounce: { scroll: 50, resize: 0 } }}
      >
        <ambientLight intensity={0.8} />
        <directionalLight position={[5, 10, 5]} intensity={0.8} castShadow />
        <pointLight position={[10, 10, 10]} intensity={0.7} />
        <pointLight position={[-10, -10, -10]} intensity={0.5} />
        <hemisphereLight args={['#ffffff', '#8888ff', 0.6]} />
        
        {/* Coordinate system grid */}
        <Grid
          args={GRID_ARGS}
          position={[0, 0, 0]}
          cellColor={GRID_CELL_COLOR}
          sectionColor={GRID_SECTION_COLOR}
          fadeDistance={GRID_FADE_DISTANCE}
          cellSize={GRID_CELL_SIZE}
        />

        {/* Axis direction indicator */}
        <AxisIndicator />
        
        {/* Force plates and vectors */}
        {forcePlates.map((plate, index) => {
          const position = platePositions[plate.id];
          const isHighlighted = plate.id === hoveredPlateId;
          
          if (!position) return null;
          
          // Use center of pressure (Cx, Cy) for vector origin, fallback to center if no data
          const vectorPosition: [number, number, number] = plate.lastData
            ? [
                position[0] + plate.lastData.cx * COP_SCALE,
                position[1] + FORCE_PLATE_THICKNESS / 2, // top surface of plate
                position[2] + plate.lastData.cy * COP_SCALE
              ]
            : [
                position[0],
                position[1] + FORCE_PLATE_THICKNESS / 2,
                position[2]
              ];
              
          return (
            <group key={plate.id}>
              <ForcePlateVisual 
                position={position} 
                isHighlighted={isHighlighted}
                plate={plate}
              />
              {plate.isReading && plate.lastData && (
                <ForceVector 
                  position={vectorPosition} 
                  forceData={plate.lastData} 
                  color={index % 2 === 0 ? "#0EA5E9" : "#9b87f5"} 
                />
              )}
            </group>
          );
        })}
        
        {/* Controls */}
        <OrbitControls 
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          dampingFactor={0.1}
        />
      </Canvas>
    </div>
  );
};

export default ForceVisualization;
