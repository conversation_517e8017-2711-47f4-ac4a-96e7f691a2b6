import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useSerial, ForcePlate, DataFormat } from '@/context/SerialContext';
import { AlertCircle, Trash2, Save, Pause, Play, Plus, RefreshCw, Disc, Move, Target, ChevronDown } from 'lucide-react';
import { dispatchPlateHoverEvent } from '@/lib/plateHoverEvent';
import { toast } from '@/components/ui/use-toast';

const ConnectionStatus = ({ isConnected }: { isConnected: boolean }) => {
  return (
    <Badge variant={isConnected ? "default" : "outline"} className={isConnected ? "bg-green-500" : "bg-red-500/50"}>
      {isConnected ? 'Connected' : 'Disconnected'}
    </Badge>
  );
};

const ForcePlateSidebarItem = ({ plate }: { plate: ForcePlate }) => {
  const { startReading, stopReading, disconnectPort, saveData, clearData, zeroPlate, connectToPort, availablePorts, forcePlates } = useSerial();
  const [showPositionControls, setShowPositionControls] = useState(false);

  const handleToggleReading = async () => {
    if (plate.isReading) {
      await stopReading(plate.id);
    } else {
      await startReading(plate.id);
    }
  };

  const handleStartFresh = async () => {
    // Clear existing data first, then start reading
    clearData(plate.id);
    await startReading(plate.id);
  };

  const handleSaveData = async (format: DataFormat) => {
    await saveData(plate.id, format);
  };

  const handleZeroPlate = async () => {
    await zeroPlate(plate.id);
  };

  const handleReconnect = async () => {
    // First, try to find a matching port for this plate by product name
    let matchingPort = availablePorts.find(port => {
      const info = port.getInfo();
      return info.usbProductName === plate.name ||
             (info.usbProductName && plate.name.includes(info.usbProductName));
    });

    // If no exact match by name, try to find any unconnected port
    if (!matchingPort) {
      const connectedPortIds = new Set(
        forcePlates
          .filter(p => p.isConnected && p.port)
          .map(p => p.port!.getInfo().usbProductId)
      );

      matchingPort = availablePorts.find(port => {
        const info = port.getInfo();
        return !connectedPortIds.has(info.usbProductId);
      });
    }

    if (matchingPort) {
      await connectToPort(matchingPort);
    } else {
      // No available ports found
      toast({
        title: "No Available Ports",
        description: "Please make sure the device is connected and try refreshing ports.",
        variant: "destructive",
      });
    }
  };

  // Hover handlers for highlighting
  const handleMouseEnter = () => {
    dispatchPlateHoverEvent(plate.id);
  };

  const handleMouseLeave = () => {
    dispatchPlateHoverEvent(null);
  };

  return (
    <div
      className="p-2 rounded-lg border border-border mb-2 hover:bg-accent/20 transition-colors"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-medium">{plate.name}</h3>
        <ConnectionStatus isConnected={plate.isConnected} />
      </div>

      <div className="grid grid-cols-2 gap-2 mt-3">
        {plate.isReading ? (
          <Button
            variant="outline"
            size="sm"
            onClick={handleToggleReading}
            disabled={!plate.isConnected}
            className="bg-red-900/20"
          >
            <Pause className="mr-1 h-4 w-4" />
            Stop
          </Button>
        ) : plate.data.length > 0 ? (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                disabled={!plate.isConnected}
                className="bg-green-900/20"
              >
                <Play className="mr-1 h-4 w-4" />
                Start
                <ChevronDown className="ml-1 h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={handleToggleReading}>
                Continue Recording
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleStartFresh}>
                Start Fresh (Clear Data)
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          <Button
            variant="outline"
            size="sm"
            onClick={handleToggleReading}
            disabled={!plate.isConnected}
            className="bg-green-900/20"
          >
            <Play className="mr-1 h-4 w-4" />
            Start
          </Button>
        )}

        {plate.isConnected ? (
          <Button
            variant="outline"
            size="sm"
            onClick={async () => await disconnectPort(plate.id)}
          >
            <Disc className="mr-1 h-4 w-4" />
            Disconnect
          </Button>
        ) : (
          <Button
            variant="outline"
            size="sm"
            onClick={handleReconnect}
            className="bg-blue-900/20"
          >
            <Plus className="mr-1 h-4 w-4" />
            Reconnect
          </Button>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              disabled={plate.data.length === 0}
            >
              <Save className="mr-1 h-4 w-4" />
              Save Data
              <ChevronDown className="ml-1 h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => handleSaveData('json')}>
              Save as JSON
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleSaveData('csv')}>
              Save as CSV
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button
          variant="outline"
          size="sm"
          onClick={() => clearData(plate.id)}
          disabled={plate.data.length === 0}
        >
          <Trash2 className="mr-1 h-4 w-4" />
          Clear
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={handleZeroPlate}
          disabled={!plate.isConnected}
        >
          <Target className="mr-1 h-4 w-4" />
          Zero
        </Button>

        <Button
          variant="outline"
          size="sm"
          className="col-span-2"
          onClick={() => setShowPositionControls(!showPositionControls)}
        >
          <Move className="mr-1 h-4 w-4" />
          {showPositionControls ? 'Hide Position' : 'Position Controls'}
        </Button>
      </div>

      {showPositionControls && (
        <div className="mt-3 p-2 bg-card/50 rounded-md">
          <p className="text-xs text-muted-foreground mb-2">
            Position controls will be added in a future update
          </p>
        </div>
      )}

      {plate.isReading && plate.lastData && (
        <div className="mt-3 p-2 bg-card/50 rounded-md">
          <div className="text-xs text-muted-foreground mb-1">Last reading:</div>
          <div className="grid grid-cols-3 gap-1">
            <div className="value-box text-xs">Fx: {plate.lastData.fx.toFixed(1)}</div>
            <div className="value-box text-xs">Fy: {plate.lastData.fy.toFixed(1)}</div>
            <div className="value-box text-xs">Fz: {plate.lastData.fz.toFixed(1)}</div>
          </div>
        </div>
      )}

      <div className="text-xs text-muted-foreground mt-2">
        {plate.data.length.toLocaleString()} samples captured
        {plate.isReading && plate.data.length > 0 && (
          <span className="ml-2 text-green-400">● Live</span>
        )}
        {plate.data.length >= 100000 && (
          <div className="text-xs text-amber-400 mt-1">
            ⚠ Sample limit reached (100k)
          </div>
        )}
      </div>
    </div>
  );
};

const AppSidebar = () => {
  const { availablePorts, forcePlates, isScanning, scanForPorts, connectToPort } = useSerial();

  const handleConnectPort = async () => {
    try {
      const port = await navigator.serial.requestPort();
      if (port) {
        await connectToPort(port);
      }
    } catch (error) {
      console.error('Failed to select a port', error);
    }
  };

  return (
    <div className="flex flex-col h-full w-full bg-sidebar text-sidebar-foreground border-r border-sidebar-border">
      {/* Sidebar Header */}
      <div className="flex items-center p-4 border-b border-sidebar-border">
        <div className="flex-1">
          <h1 className="text-lg font-bold text-sidebar-foreground">Acinotech Force Vista</h1>
          <p className="text-xs text-sidebar-foreground/70">Force Plate Visualization</p>
        </div>
      </div>

      {/* Sidebar Content */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-sidebar-foreground">Connected Devices</h3>
            <Badge variant="outline" className="text-xs">
              {forcePlates.filter(p => p.isReading).length} Active • {forcePlates.length} Connected
            </Badge>
          </div>
          <div>
            {forcePlates.length === 0 ? (
              <div className="py-8 text-center text-muted-foreground">
                <AlertCircle className="mx-auto h-8 w-8 mb-2 text-muted-foreground/70" />
                <p className="text-sm">No force plates connected</p>
                <p className="text-xs mt-1">Connect a device to get started</p>
              </div>
            ) : (
              forcePlates.map(plate => (
                <ForcePlateSidebarItem key={plate.id} plate={plate} />
              ))
            )}

            <Button
              className="w-full mt-2"
              variant="default"
              onClick={handleConnectPort}
            >
              <Plus className="mr-1 h-4 w-4" />
              Connect Force Plate
            </Button>
          </div>
        </div>

        <div className="mb-6">
          <h3 className="text-sm font-medium text-sidebar-foreground mb-3">Available Ports</h3>
          <div>
            {availablePorts.length === 0 ? (
              <p className="text-sm text-muted-foreground text-center py-2">No available ports</p>
            ) : (
              <div className="space-y-2">
                {availablePorts.map((port, index) => {
                  const info = port.getInfo();
                  const name = info.usbProductName || `Port ${index + 1}`;
                  const isConnected = forcePlates.some(p =>
                    p.port && p.port.getInfo().usbProductId === info.usbProductId
                  );

                  return (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm">{name}</span>
                      {isConnected ? (
                        <Badge variant="outline" className="text-xs">In use</Badge>
                      ) : (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => connectToPort(port)}
                        >
                          Connect
                        </Button>
                      )}
                    </div>
                  );
                })}
              </div>
            )}

            <Button
              variant="outline"
              size="sm"
              className="w-full mt-2"
              onClick={scanForPorts}
              disabled={isScanning}
            >
              <RefreshCw className={`mr-1 h-4 w-4 ${isScanning ? 'animate-spin' : ''}`} />
              {isScanning ? 'Scanning...' : 'Refresh Ports'}
            </Button>
          </div>
        </div>
      </div>

      {/* Sidebar Footer */}
      <div className="p-4 border-t border-sidebar-border">
        <p className="text-xs text-sidebar-foreground/70 text-center">
          Force Vista - Monitoring at 1kHz
        </p>
      </div>
    </div>
  );
};

export default AppSidebar;
